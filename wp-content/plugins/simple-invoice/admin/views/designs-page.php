<?php
/**
 * Designs Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Variables are passed from the admin class
?>

<div class="wrap si-designs-page">
    <!-- Header Section -->
    <div class="si-page-header">
        <div class="si-header-content">
            <div class="si-header-title">
                <h1 class="si-page-title">
                    <span class="dashicons dashicons-admin-appearance"></span>
                    <?php echo esc_html__('Invoice Designs', 'simple-invoice'); ?>
                </h1>
                <p class="si-page-subtitle"><?php echo esc_html__('Manage visual designs for your invoices', 'simple-invoice'); ?></p>
            </div>
            <div class="si-header-actions">
                <a href="#" class="si-btn si-btn-primary si-add-design-btn">
                    <span class="dashicons dashicons-plus-alt"></span>
                    <?php echo esc_html__('Add Custom Design', 'simple-invoice'); ?>
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="si-quick-stats">
        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-blue">
                <span class="dashicons dashicons-admin-appearance"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html(count($available_designs)); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Total Designs', 'simple-invoice'); ?></span>
            </div>
        </div>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-green">
                <span class="dashicons dashicons-yes"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number">3</span>
                <span class="si-stat-label"><?php echo esc_html__('Built-in', 'simple-invoice'); ?></span>
            </div>
        </div>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-orange">
                <span class="dashicons dashicons-star-filled"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html(max(0, count($available_designs) - 3)); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Custom', 'simple-invoice'); ?></span>
            </div>
        </div>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-purple">
                <span class="dashicons dashicons-upload"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html__('Easy', 'simple-invoice'); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Upload', 'simple-invoice'); ?></span>
            </div>
        </div>
    </div>

    <!-- Designs Content -->
    <div class="si-designs-content">
        <div class="si-section-header">
            <h2 class="si-section-title">
                <span class="dashicons dashicons-admin-appearance"></span>
                <?php echo esc_html__('Available Designs', 'simple-invoice'); ?>
            </h2>
            <p class="si-section-description">
                <?php echo esc_html__('Choose from professional invoice designs or create your own custom designs.', 'simple-invoice'); ?>
            </p>
        </div>

        <div class="si-designs-grid">
            <?php foreach ($available_designs as $design_id => $design): ?>
                <div class="si-design-card" data-design-id="<?php echo esc_attr($design_id); ?>">
                    <div class="si-design-preview">
                        <?php if (!empty($design['preview_url'])): ?>
                            <img src="<?php echo esc_url($design['preview_url']); ?>" alt="<?php echo esc_attr($design['name']); ?>" />
                        <?php else: ?>
                            <div class="si-design-placeholder">
                                <span class="dashicons dashicons-admin-appearance"></span>
                                <span class="si-design-name"><?php echo esc_html($design['name']); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="si-design-info">
                        <h3 class="si-design-title"><?php echo esc_html($design['name']); ?></h3>
                        <p class="si-design-description"><?php echo esc_html($design['description']); ?></p>
                        <div class="si-design-meta">
                            <span class="si-design-id">ID: <?php echo esc_html($design_id); ?></span>
                            <span class="si-design-type" data-type="<?php echo in_array($design_id, ['classic', 'modern', 'minimal']) ? 'built-in' : 'custom'; ?>">
                                <?php echo in_array($design_id, ['classic', 'modern', 'minimal']) ?
                                    esc_html__('Built-in', 'simple-invoice') :
                                    esc_html__('Custom', 'simple-invoice'); ?>
                            </span>
                        </div>
                        <div class="si-design-actions">
                            <button type="button" class="si-btn si-btn-primary si-btn-sm si-preview-design" data-design-id="<?php echo esc_attr($design_id); ?>">
                                <span class="dashicons dashicons-visibility"></span>
                                <?php echo esc_html__('Preview', 'simple-invoice'); ?>
                            </button>
                            <?php if (!in_array($design_id, ['classic', 'modern', 'minimal'])): ?>
                                <button type="button" class="si-btn si-btn-secondary si-btn-sm si-edit-design" data-design-id="<?php echo esc_attr($design_id); ?>">
                                    <span class="dashicons dashicons-edit"></span>
                                    <?php echo esc_html__('Edit', 'simple-invoice'); ?>
                                </button>
                                <button type="button" class="si-btn si-btn-danger si-btn-sm si-delete-design" data-design-id="<?php echo esc_attr($design_id); ?>">
                                    <span class="dashicons dashicons-trash"></span>
                                    <?php echo esc_html__('Delete', 'simple-invoice'); ?>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
            
            <!-- Add Custom Design Card -->
            <div class="si-design-card si-add-design-card">
                <div class="si-design-preview">
                    <div class="si-design-placeholder">
                        <span class="dashicons dashicons-plus-alt"></span>
                        <span class="si-design-name"><?php echo esc_html__('Add Custom', 'simple-invoice'); ?></span>
                    </div>
                </div>
                <div class="si-design-info">
                    <h3 class="si-design-title"><?php echo esc_html__('Create Custom Design', 'simple-invoice'); ?></h3>
                    <p class="si-design-description"><?php echo esc_html__('Add your own custom invoice design by creating a new design folder or uploading files.', 'simple-invoice'); ?></p>
                    <div class="si-design-actions">
                        <a href="#" class="si-btn si-btn-primary si-btn-sm si-add-design-btn">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php echo esc_html__('Add Design', 'simple-invoice'); ?>
                        </a>
                        <a href="#" class="si-btn si-btn-secondary si-btn-sm si-view-guide">
                            <span class="dashicons dashicons-book"></span>
                            <?php echo esc_html__('View Guide', 'simple-invoice'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Design Modal -->
<div id="si-design-modal" class="si-modal si-modal-large" style="display: none;">
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2 id="si-design-modal-title"><?php echo esc_html__('Add Custom Design', 'simple-invoice'); ?></h2>
        </div>

        <div class="si-modal-body">
            <form id="si-design-form" enctype="multipart/form-data">
                <input type="hidden" id="si-design-id" name="design_id" value="" />

                <div class="si-design-form-sections">
                    <!-- Basic Information -->
                    <div class="si-form-section">
                        <h3><?php echo esc_html__('Basic Information', 'simple-invoice'); ?></h3>

                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="si-design-name"><?php echo esc_html__('Design Name', 'simple-invoice'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <input type="text"
                                           id="si-design-name"
                                           name="name"
                                           class="regular-text"
                                           required
                                           placeholder="<?php echo esc_attr__('e.g., Corporate Blue, Elegant Black', 'simple-invoice'); ?>" />
                                    <p class="description"><?php echo esc_html__('Give your design a unique name.', 'simple-invoice'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="si-design-description"><?php echo esc_html__('Description', 'simple-invoice'); ?></label>
                                </th>
                                <td>
                                    <textarea id="si-design-description"
                                              name="description"
                                              class="large-text"
                                              rows="3"
                                              placeholder="<?php echo esc_attr__('Describe your design style and features...', 'simple-invoice'); ?>"></textarea>
                                    <p class="description"><?php echo esc_html__('Brief description of your design.', 'simple-invoice'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Design Files -->
                    <div class="si-form-section">
                        <h3><?php echo esc_html__('Design Files', 'simple-invoice'); ?></h3>
                        <p class="description"><?php echo esc_html__('Upload your design files or create them manually in the designs folder.', 'simple-invoice'); ?></p>

                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="si-design-template"><?php echo esc_html__('Template File', 'simple-invoice'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <input type="file" id="si-design-template" name="template_file" accept=".php" />
                                    <p class="description"><?php echo esc_html__('Upload template.php file (required)', 'simple-invoice'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="si-design-style"><?php echo esc_html__('Style File', 'simple-invoice'); ?></label>
                                </th>
                                <td>
                                    <input type="file" id="si-design-style" name="style_file" accept=".css" />
                                    <p class="description"><?php echo esc_html__('Upload style.css file (optional)', 'simple-invoice'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="si-design-preview"><?php echo esc_html__('Preview Image', 'simple-invoice'); ?></label>
                                </th>
                                <td>
                                    <input type="file" id="si-design-preview" name="preview_file" accept=".jpg,.jpeg,.png" />
                                    <p class="description"><?php echo esc_html__('Upload preview image (optional, recommended size: 400x300px)', 'simple-invoice'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </form>
        </div>

        <div class="si-modal-footer">
            <button type="button" class="button button-primary" id="si-save-design"><?php echo esc_html__('Save Changes', 'simple-invoice'); ?></button>
            <button type="button" class="button button-secondary" id="si-close-modal-btn"><?php echo esc_html__('Close', 'simple-invoice'); ?></button>
        </div>
    </div>
</div>

<style>
/* Designs Page Professional Styling */
.si-designs-page {
    background: #ffffff;
    margin: 0 -20px;
    padding: 0;
    min-height: 100vh;
}

/* Page Header */
.si-page-header {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    padding: 30px 20px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.si-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.si-header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    position: relative;
    z-index: 1;
}

.si-header-title {
    flex: 1;
}

.si-page-title {
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 15px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.si-page-title .dashicons {
    font-size: 36px;
    width: 36px;
    height: 36px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    padding: 8px;
}

.si-page-subtitle {
    margin: 0;
    font-size: 16px;
    color: #ffffff;
    opacity: 0.9;
    font-weight: 400;
}

.si-header-actions {
    flex-shrink: 0;
}

/* Modern Buttons */
.si-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.si-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.si-btn:hover::before {
    left: 100%;
}

.si-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
    text-decoration: none;
}

.si-btn:active {
    transform: translateY(0);
}

.si-btn-primary {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: white;
}

.si-btn-primary:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    color: white;
}

.si-btn-secondary {
    background: linear-gradient(135deg, #e7e7e7 0%, #ffffff 100%);
    color: #000000;
}

.si-btn-secondary:hover {
    background: linear-gradient(135deg, #ffffff 0%, #e7e7e7 100%);
    color: #000000;
}

.si-btn-danger {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: white;
}

.si-btn-danger:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    color: white;
}

.si-btn-sm {
    padding: 8px 14px;
    font-size: 12px;
    border-radius: 6px;
}

.si-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.si-btn-sm .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Enhanced Quick Stats */
.si-quick-stats {
    max-width: 1200px;
    margin: 0 auto 40px auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.si-stat-item {
    background: white;
    border-radius: 16px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.si-stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--stat-color, #667eea);
}

.si-stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.si-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
}

.si-stat-icon .dashicons {
    font-size: 28px;
    width: 28px;
    height: 28px;
    color: white;
    z-index: 1;
}

.si-stat-blue {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    --stat-color: #f47a45;
}

.si-stat-green {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    --stat-color: #f47a45;
}

.si-stat-orange {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    --stat-color: #f47a45;
}

.si-stat-purple {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    --stat-color: #f47a45;
}

.si-stat-info {
    flex: 1;
}

.si-stat-number {
    display: block;
    font-size: 28px;
    font-weight: 700;
    color: #333;
    line-height: 1;
    margin-bottom: 6px;
}

.si-stat-label {
    display: block;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* Designs Content */
.si-designs-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section Headers */
.si-section-header {
    margin-bottom: 30px;
    text-align: center;
}

.si-section-title {
    margin: 0 0 12px 0;
    font-size: 28px;
    font-weight: 700;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.si-section-title .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 50%;
    padding: 8px;
}

.si-section-description {
    margin: 0;
    color: #666;
    font-size: 16px;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Enhanced Designs Grid */
.si-designs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.si-design-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s ease;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    position: relative;
    cursor: pointer;
}

.si-design-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.si-design-card:hover::before {
    opacity: 1;
}

.si-design-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.si-design-card.si-add-design-card {
    border: 3px dashed #ddd;
    background: #fafafa;
}

.si-design-card.si-add-design-card:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.si-design-preview {
    height: 220px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.si-design-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.si-design-card:hover .si-design-preview img {
    transform: scale(1.05);
}

.si-design-placeholder {
    text-align: center;
    color: #666;
    padding: 20px;
}

.si-design-placeholder .dashicons {
    font-size: 64px;
    margin-bottom: 15px;
    opacity: 0.6;
    color: #667eea;
}

.si-design-placeholder .si-design-name {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.si-design-info {
    padding: 25px;
}

.si-design-title {
    margin: 0 0 10px 0;
    font-size: 20px;
    font-weight: 700;
    color: #333;
}

.si-design-description {
    margin: 0 0 15px 0;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

.si-design-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0;
    padding: 10px 0;
    border-top: 1px solid #f0f0f0;
}

.si-design-id {
    font-size: 11px;
    color: #999;
    background: #f1f1f1;
    padding: 4px 8px;
    border-radius: 12px;
    font-family: monospace;
}

.si-design-type {
    font-size: 11px;
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.si-design-type[data-type="built-in"] {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.si-design-type[data-type="custom"] {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.si-design-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.si-design-actions .si-btn {
    flex: 1;
    justify-content: center;
    min-width: 0;
    font-size: 12px;
    padding: 10px 12px;
}

/* File Upload Styling */
input[type="file"] {
    padding: 15px;
    border: 3px dashed #ddd;
    border-radius: 12px;
    background: #fafafa;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

input[type="file"]:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

input[type="file"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Modal Enhancements */
.si-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 100000;
    display: flex !important;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(8px);
    padding: 20px;
    box-sizing: border-box;
}

.si-modal-content {
    background: #ffffff;
    border-radius: 20px;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow: auto;
    box-shadow: 0 25px 80px rgba(0,0,0,0.4);
    animation: modalSlideIn 0.4s ease;
    margin: auto;
    position: relative;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Ensure modal is always centered */
.si-modal[style*="display: block"],
.si-modal[style*="display: flex"] {
    display: flex !important;
}

/* Prevent body scroll when modal is open */
body.modal-open {
    overflow: hidden;
}

/* Responsive modal behavior */
@media (max-width: 768px) {
    .si-modal {
        padding: 10px;
    }

    .si-modal-content {
        max-width: 95vw;
        max-height: 95vh;
        border-radius: 15px;
    }

    .si-modal-large .si-modal-content {
        max-width: 95vw;
    }

    .si-modal-header {
        padding: 20px;
        border-radius: 15px 15px 0 0;
    }

    .si-modal-header h2 {
        font-size: 20px;
    }

    .si-modal-body {
        padding: 20px;
    }

    .si-modal-footer {
        padding: 20px;
        border-radius: 0 0 15px 15px;
    }
}

.si-modal-large .si-modal-content {
    max-width: 900px;
}

.si-modal-header {
    padding: 25px 30px;
    border-bottom: 1px solid #e7e7e7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    border-radius: 20px 20px 0 0;
}

.si-modal-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: #ffffff;
}

.si-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #ffffff;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.3s ease;
    position: relative;
    z-index: 1001;
    line-height: 1;
    width: 34px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-modal-close:hover {
    background: rgba(255,255,255,0.2);
}

.si-modal-close:focus {
    outline: 2px solid rgba(255,255,255,0.5);
    outline-offset: 2px;
}

/* Ensure close buttons are clickable */
.si-modal-close {
    pointer-events: auto !important;
    user-select: none;
}

/* Hide any close buttons in header for regular modals */
.si-modal-header .si-modal-close {
    display: none !important;
}

/* Show close button in preview modal header */
#si-preview-modal .si-modal-header .si-modal-close {
    display: flex !important;
}

/* Preview modal specific styling */
#si-preview-modal .si-modal-header {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    padding: 20px 25px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#si-preview-modal .si-modal-header h2 {
    color: #ffffff;
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.si-preview-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

#si-preview-modal .si-modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: #ffffff;
    font-size: 20px;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

#si-preview-modal .si-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Preview modal content styling */
#si-preview-modal .si-modal-content {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

/* Style footer close button */
.si-modal-footer .si-modal-close {
    margin-left: 10px;
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.si-modal-footer .si-modal-close:hover {
    background: #5a6268;
}

/* Hide any WordPress default close buttons or other close elements */
.si-modal .close,
.si-modal .modal-close,
.si-modal .wp-dialog-close,
.si-modal-header .close,
.si-modal-header .modal-close {
    display: none !important;
}

/* Ensure modal footer is visible and buttons are properly aligned */
.si-modal-footer {
    padding: 20px 30px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
}

/* Ensure modal is hidden by default */
#si-design-modal {
    display: none !important;
}

/* Only show modal when explicitly shown */
#si-design-modal.si-modal-show {
    display: flex !important;
}

.si-modal-body {
    padding: 30px;
}

.si-modal-footer {
    padding: 25px 30px;
    border-top: 1px solid #e7e7e7;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    background: #e7e7e7;
    border-radius: 0 0 20px 20px;
}

/* Form Sections */
.si-form-section {
    margin-bottom: 30px;
    padding: 25px;
    background: #fafafa;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.si-form-section h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.si-form-section .description {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .si-designs-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .si-quick-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .si-page-title {
        font-size: 24px;
    }

    .si-header-content {
        flex-direction: column;
        text-align: center;
    }

    .si-design-actions {
        flex-direction: column;
    }

    .si-design-actions .si-btn {
        flex: none;
    }
}

@media (max-width: 480px) {
    .si-quick-stats {
        grid-template-columns: 1fr;
    }

    .si-stat-item {
        padding: 20px;
    }

    .si-design-info {
        padding: 20px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    console.log('Simple Invoice Designs JS loaded');

    // Enhanced animations and interactions
    initializeDesignInteractions();

    function initializeDesignInteractions() {
        console.log('Initializing design interactions...');

        // Add design modal with animation
        $('.si-add-design-btn').on('click', function(e) {
            e.preventDefault();
            console.log('Add design button clicked');
            showModal('#si-design-modal');
        });

        // Close modal handler for our specific close button
        $('#si-close-modal-btn').on('click', function(e) {
            e.preventDefault();
            console.log('Close button clicked');
            closeDesignModal();
        });

        // Close modal when clicking backdrop
        $('#si-design-modal').on('click', function(e) {
            if (e.target === this) {
                console.log('Backdrop clicked');
                closeDesignModal();
            }
        });

        // Prevent modal content clicks from closing modal
        $('#si-design-modal .si-modal-content').on('click', function(e) {
            e.stopPropagation();
        });

        // Design card hover effects
        $('.si-design-card').on('mouseenter', function() {
            $(this).find('.si-design-actions').slideDown(200);
        }).on('mouseleave', function() {
            $(this).find('.si-design-actions').slideUp(200);
        });

        // File upload enhancements
        $('input[type="file"]').on('change', function() {
            var fileName = $(this).val().split('\\').pop();
            if (fileName) {
                $(this).next('.description').html('<strong>Selected:</strong> ' + fileName);
            }
        });

        console.log('Design interactions initialized');
    }

    // Dedicated function to close the design modal
    function closeDesignModal() {
        console.log('Closing design modal...');
        $('#si-design-modal').fadeOut(300, function() {
            $(this).removeClass('si-modal-show');
            $('body').removeClass('modal-open');
            // Reset form
            if ($('#si-design-form').length) {
                $('#si-design-form')[0].reset();
                $('#si-design-id').val('');
                $('#si-design-modal-title').text('<?php echo esc_js(__('Add Custom Design', 'simple-invoice')); ?>');
            }
            console.log('Design modal closed');
        });
    }

    function showModal(modalId) {
        console.log('Showing modal:', modalId);
        $(modalId).addClass('si-modal-show').fadeIn(300);
        $('body').addClass('modal-open');

        // Remove any unwanted close buttons from header after modal is shown
        setTimeout(function() {
            $(modalId + ' .si-modal-header .si-modal-close').remove();
            $(modalId + ' .si-modal-header .close').remove();
            $(modalId + ' .si-modal-header .modal-close').remove();
        }, 100);
    }

    function hideModal(modalId) {
        console.log('Hiding modal:', modalId);
        $(modalId).fadeOut(300, function() {
            $('body').removeClass('modal-open');
            console.log('Modal hidden:', modalId);

            // Reset form if it's the design modal
            if (modalId === '#si-design-modal') {
                $('#si-design-form')[0].reset();
                $('#si-design-id').val('');
                $('#si-design-modal-title').text('<?php echo esc_js(__('Add Custom Design', 'simple-invoice')); ?>');
            }
        });
    }

    // Design Guide Modal
    $('.si-view-guide').on('click', function(e) {
        e.preventDefault();
        // Create and show design guide modal
        showDesignGuideModal();
    });

    function showDesignGuideModal() {
        var modalHtml = `
        <div id="si-design-guide-modal" class="si-modal" style="display: block;">
            <div class="si-modal-content si-modal-large">
                <div class="si-modal-header">
                    <h2>Custom Design Guide</h2>
                    <span class="si-modal-close">&times;</span>
                </div>
                <div class="si-modal-body">
                    <div class="si-guide-content">
                        <h3>How to Add Custom Designs</h3>

                        <div class="si-guide-step">
                            <h4><span class="si-step-number">1</span> Create Design Folder</h4>
                            <p>Create a new folder in:</p>
                            <code>wp-content/plugins/simple-invoice/designs/your-design-name/</code>
                        </div>

                        <div class="si-guide-step">
                            <h4><span class="si-step-number">2</span> Required Files</h4>
                            <ul>
                                <li><strong>template.php</strong> - Main template file (required)</li>
                                <li><strong>style.css</strong> - Custom CSS styles (optional)</li>
                                <li><strong>preview.jpg</strong> - Preview image (optional)</li>
                            </ul>
                        </div>

                        <div class="si-guide-step">
                            <h4><span class="si-step-number">3</span> Quick Start</h4>
                            <ol>
                                <li>Copy an existing design folder (e.g., "classic")</li>
                                <li>Rename it to your design name</li>
                                <li>Modify template.php with your custom HTML/CSS</li>
                                <li>Refresh this page to see your new design</li>
                            </ol>
                        </div>

                        <div class="si-guide-actions">
                            <a href="<?php echo esc_url(SI_PLUGIN_URL . 'DESIGN-TEMPLATES-GUIDE.md'); ?>" target="_blank" class="si-btn si-btn-primary">
                                <span class="dashicons dashicons-external"></span>
                                View Full Documentation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>`;

        $('body').append(modalHtml);

        // Modal content click prevention (handled by document event handlers above)

        $('#si-design-guide-modal .si-modal-content').on('click', function(e) {
            e.stopPropagation();
        });
    }

    // Save design with loading state
    $('#si-save-design').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.html();

        // Show loading state
        $btn.html('<span class="dashicons dashicons-update-alt" style="animation: spin 1s linear infinite;"></span> Saving...').prop('disabled', true);

        // Simulate save process
        setTimeout(function() {
            $btn.html('<span class="dashicons dashicons-yes"></span> Saved!').removeClass('button-primary').addClass('button-secondary');

            setTimeout(function() {
                $btn.html(originalText).prop('disabled', false).removeClass('button-secondary').addClass('button-primary');
                hideModal('#si-design-modal');
                showNotification('Design saved successfully!', 'success');
            }, 1000);
        }, 2000);
    });

    // Preview design with modal
    $('.si-preview-design').on('click', function() {
        var designId = $(this).data('design-id');
        showDesignPreview(designId);
    });

    // Delete design with confirmation
    $('.si-delete-design').on('click', function() {
        var designId = $(this).data('design-id');
        var designName = $(this).closest('.si-design-card').find('.si-design-title').text();

        showDeleteConfirmation(designId, designName);
    });

    function showDesignPreview(designId) {
        var $btn = $('.si-preview-design[data-design-id="' + designId + '"]');
        var originalText = $btn.html();

        // Show loading state
        $btn.html('<span class="dashicons dashicons-update-alt" style="animation: spin 1s linear infinite;"></span> Loading...').prop('disabled', true);

        // Create preview modal with iframe
        var previewHtml = `
        <div id="si-preview-modal" class="si-modal" style="display: flex;">
            <div class="si-modal-content" style="width: 95vw; height: 95vh; max-width: 1200px;">
                <div class="si-modal-header">
                    <h2>Design Preview: ${designId.charAt(0).toUpperCase() + designId.slice(1)}</h2>
                    <div class="si-preview-actions">
                        <button type="button" class="si-btn si-btn-secondary si-btn-sm" onclick="openPreviewInNewTab('${designId}')">
                            <span class="dashicons dashicons-external"></span>
                            Open in New Tab
                        </button>
                        <span class="si-modal-close">&times;</span>
                    </div>
                </div>
                <div class="si-modal-body" style="padding: 0; height: calc(100% - 80px);">
                    <div class="si-preview-container" style="height: 100%; position: relative;">
                        <div class="si-preview-loading" style="
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            text-align: center;
                            z-index: 10;
                        ">
                            <div class="dashicons dashicons-update-alt" style="font-size: 48px; color: #667eea; animation: spin 1s linear infinite; margin-bottom: 15px;"></div>
                            <p>Loading preview...</p>
                        </div>
                        <iframe
                            id="si-preview-frame"
                            src="${getPreviewUrl(designId)}"
                            style="
                                width: 100%;
                                height: 100%;
                                border: none;
                                border-radius: 0 0 20px 20px;
                                opacity: 0;
                                transition: opacity 0.3s ease;
                            "
                            onload="handlePreviewLoad(this)"
                        ></iframe>
                    </div>
                </div>
            </div>
        </div>`;

        $('body').append(previewHtml);

        // Reset button after modal is shown
        setTimeout(function() {
            $btn.html(originalText).prop('disabled', false);
        }, 500);

        // Modal content click prevention (handled by document event handlers above)
        $('#si-preview-modal .si-modal-content').on('click', function(e) {
            e.stopPropagation();
        });

        // Close preview modal when close button is clicked
        $('#si-preview-modal .si-modal-close').on('click', function(e) {
            e.preventDefault();
            closePreviewModal();
        });

        // Close preview modal when clicking backdrop
        $('#si-preview-modal').on('click', function(e) {
            if (e.target === this) {
                closePreviewModal();
            }
        });

        // Close preview modal with Escape key
        $(document).on('keydown.si-preview-modal', function(e) {
            if (e.keyCode === 27) { // Escape key
                closePreviewModal();
            }
        });
    }

    // Function to close the preview modal
    function closePreviewModal() {
        // Remove the keydown event listener
        $(document).off('keydown.si-preview-modal');

        $('#si-preview-modal').fadeOut(300, function() {
            $(this).remove(); // Remove the modal from DOM since it's dynamically created
        });
    }

    function getPreviewUrl(designId) {
        // Create a preview URL that will show the design with sample data
        var baseUrl = '<?php echo admin_url("admin-ajax.php"); ?>';
        return baseUrl + '?action=si_preview_design&design_id=' + designId + '&_wpnonce=<?php echo wp_create_nonce("si_preview_design"); ?>';
    }

    window.handlePreviewLoad = function(iframe) {
        // Hide loading and show iframe
        $(iframe).closest('.si-preview-container').find('.si-preview-loading').fadeOut(300);
        $(iframe).css('opacity', '1');
    };

    window.openPreviewInNewTab = function(designId) {
        window.open(getPreviewUrl(designId), '_blank');
    };

    function showDeleteConfirmation(designId, designName) {
        var confirmHtml = `
        <div id="si-delete-modal" class="si-modal" style="display: flex;">
            <div class="si-modal-content" style="width: 500px;">
                <div class="si-modal-header" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                    <h2>Delete Design</h2>
                    <span class="si-modal-close">&times;</span>
                </div>
                <div class="si-modal-body">
                    <div style="text-align: center; padding: 20px;">
                        <div class="dashicons dashicons-warning" style="font-size: 64px; color: #dc3545; margin-bottom: 20px;"></div>
                        <h3>Are you sure?</h3>
                        <p>You are about to delete the design "<strong>${designName}</strong>".</p>
                        <p style="color: #dc3545;"><strong>This action cannot be undone.</strong></p>
                    </div>
                </div>
                <div class="si-modal-footer">
                    <button type="button" class="button button-secondary si-modal-close">Cancel</button>
                    <button type="button" class="button button-primary" style="background: #dc3545; border-color: #dc3545;" onclick="confirmDelete('${designId}')">Delete Design</button>
                </div>
            </div>
        </div>`;

        $('body').append(confirmHtml);

        // Modal close handled by document event handlers above
    }

    window.confirmDelete = function(designId) {
        $('#si-delete-modal').fadeOut(300, function() {
            $(this).remove();
        });

        showNotification('Design deletion functionality will be implemented', 'info');
    };

    function showNotification(message, type = 'success') {
        var bgColor = type === 'success' ? '#56ab2f' : type === 'error' ? '#dc3545' : '#0073aa';
        var notificationHtml = `
        <div class="si-notification" style="
            position: fixed;
            top: 32px;
            right: 20px;
            background: ${bgColor};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 100001;
            animation: slideInRight 0.3s ease;
        ">
            <span class="dashicons dashicons-${type === 'success' ? 'yes' : type === 'error' ? 'no' : 'info'}" style="margin-right: 8px;"></span>
            ${message}
        </div>`;

        $('body').append(notificationHtml);

        setTimeout(function() {
            $('.si-notification').fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    // Add CSS animations
    $('<style>').text(`
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        .modal-open {
            overflow: hidden;
        }
        .si-design-actions {
            display: none;
        }
        .si-design-card:hover .si-design-actions {
            display: flex !important;
        }
    `).appendTo('head');

    // Make sure modal is hidden on page load
    $('#si-design-modal').hide();
    $('body').removeClass('modal-open');
});
</script>
